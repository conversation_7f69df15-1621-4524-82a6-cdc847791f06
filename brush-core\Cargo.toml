[package]
name = "brush-core"
description = "Reusable core of a POSIX/bash shell (used by brush-shell)"
version = "0.3.4"
categories.workspace = true
edition.workspace = true
keywords.workspace = true
license.workspace = true
readme.workspace = true
repository.workspace = true
rust-version.workspace = true

[lib]
bench = false

[lints]
workspace = true

[dependencies]
async-recursion = "1.1.1"
async-trait = "0.1.88"
brush-parser = { version = "^0.2.19", path = "../brush-parser" }
cached = "0.56.0"
cfg-if = "1.0.1"
chrono = "0.4.41"
clap = { version = "4.5.40", features = ["derive", "wrap_help"] }
fancy-regex = "0.16.1"
futures = "0.3.31"
indexmap = "2.10.0"
itertools = "0.14.0"
normalize-path = "0.2.1"
rand = "0.9.2"
rpds = "1.1.1"
strum = "0.27.2"
strum_macros = "0.27.2"
thiserror = "2.0.12"
tracing = "0.1.41"

[target.'cfg(target_family = "wasm")'.dependencies]
tokio = { version = "1.47.1", features = ["io-util", "macros", "rt"] }

[target.'cfg(any(windows, unix))'.dependencies]
hostname = "0.4.1"
os_pipe = { version = "1.2.2", features = ["io_safety"] }
tokio = { version = "1.47.0", features = [
    "io-util",
    "macros",
    "process",
    "rt",
    "rt-multi-thread",
    "signal",
    "sync",
] }
uucore = { version = "0.1.0", default-features = false, features = ["format"] }

[target.'cfg(windows)'.dependencies]
dirs = "6.0.0"
whoami = "1.6.0"

[target.'cfg(unix)'.dependencies]
command-fds = "0.3.2"
nix = { version = "0.30.1", features = [
    "fs",
    "process",
    "resource",
    "signal",
    "term",
    "user",
] }
rlimit = "0.10.2"
terminfo = "0.9.0"
uzers = "0.12.1"

[target.'cfg(target_os = "linux")'.dependencies]
procfs = "0.17.0"

[target.wasm32-unknown-unknown.dependencies]
getrandom = { version = "0.3.3", features = ["wasm_js"] }
uuid = { version = "1.17.0", features = ["js"] }

[dev-dependencies]
anyhow = "1.0.98"
criterion = { version = "0.5.1", features = ["async_tokio", "html_reports"] }
pretty_assertions = { version = "1.4.1", features = ["unstable"] }

[target.'cfg(unix)'.dev-dependencies]
pprof = { version = "0.15.0", features = ["criterion", "flamegraph"] }

[[bench]]
name = "shell"
harness = false
