[workspace]
resolver = "3"
members = [
    "brush-shell",
    "brush-parser",
    "brush-core",
    "brush-interactive",
    "fuzz",
    "xtask", "hello_rust",
]
default-members = ["brush-shell"]

[workspace.package]
authors = ["re<PERSON>"]
categories = ["command-line-utilities", "development-tools"]
edition = "2024"
keywords = ["cli", "shell", "sh", "bash", "script"]
license = "MIT"
readme = "README.md"
repository = "https://github.com/reubeno/brush"
rust-version = "1.85.0"

[workspace.lints.rust]
warnings = { level = "deny" }
future_incompatible = { level = "deny" }
missing_docs = { level = "deny" }
nonstandard_style = { level = "deny" }
rust_2018_idioms = { level = "deny", priority = -1 }
unnameable_types = "deny"
unsafe_op_in_unsafe_fn = "deny"
unused_attributes = "deny"
unused_lifetimes = "deny"
unused_macro_rules = "deny"
# For now we allow unknown lints so we can opt into warnings that don't exist on the
# oldest toolchain we need to support.
unknown_lints = { level = "allow", priority = -100 }

[workspace.lints.rustdoc]
all = { level = "deny", priority = -1 }

[workspace.lints.clippy]
all = { level = "deny", priority = -1 }
pedantic = { level = "deny", priority = -1 }
cargo = { level = "deny", priority = -1 }
nursery = { level = "deny", priority = -1 }
perf = { level = "deny", priority = -1 }

# Denied rules
expect_used = "deny"
format_push_string = "deny"
panic = "deny"
panic_in_result_fn = "deny"
string_slice = "deny"
todo = "deny"
unwrap_in_result = "deny"

# Allowed rules
bool_to_int_with_if = "allow"
cognitive_complexity = "allow"
collapsible_else_if = "allow"
collapsible_if = "allow"
if_not_else = "allow"
if_same_then_else = "allow"
match_same_arms = "allow"
missing_errors_doc = "allow"
missing_panics_doc = "allow"
multiple_crate_versions = "allow"
must_use_candidate = "allow"
option_if_let_else = "allow"
redundant_closure_for_method_calls = "allow"
redundant_else = "allow"
redundant_pub_crate = "allow"
result_large_err = "allow"
similar_names = "allow"
struct_excessive_bools = "allow"

[workspace.metadata.typos]
files.extend-exclude = [
    # Exclude the changelog because it's dynamically generated.
    "/CHANGELOG.md",
    # Remove this once impending mapfile updates are merged.
    "/brush-core/src/builtins/mapfile.rs",
]

[workspace.metadata.typos.default]
extend-ignore-re = [
    # -ot is a valid binary operator
    "-ot",
    # Ignore 2-letter string literals, which show up in testing a fair bit.
    '"[a-zA-Z]{2}"',
]

[workspace.metadata.typos.default.extend-words]
# These show up in tests.
"abd" = "abd"
"hel" = "hel"
"ba" = "ba"

[profile.release]
strip = true
lto = "fat"
codegen-units = 1
panic = "abort"

[profile.bench]
inherits = "release"
strip = "debuginfo"
